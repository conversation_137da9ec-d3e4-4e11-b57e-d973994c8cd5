# Instruction Patterns: Generalized AI Template Design

## Core Philosophy

This document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.

## The Three-Part Universal Structure

Every effective AI instruction follows this exact pattern:

```
[Title] Interpretation Execute as: `{Transformation}`
```

### 1. [Title] - Purpose Declaration
- **Format**: Enclosed in square brackets `[Title]`
- **Function**: Concise, action-oriented description of the template's purpose
- **Style**: Title case, descriptive, no generic terms
- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`

### 2. Interpretation - Human-Readable Instructions
- **Goal Negation Pattern**: MUST begin with `"Your goal is not to **[action]**, but to **[transformation]**"`
- **Command Voice**: No first-person references, no conversational language
- **Clarity**: Explains the transformation in natural language
- **Ending**: MUST end with "Execute as:" leading to transformation block

### 3. `{Transformation}` - Machine-Parsable Parameters
- **Format**: JSON-like structure in backticks with curly braces
- **Components**: role, input, process, constraints, requirements, output
- **Type Safety**: All parameters must specify data types
- **Actionability**: All process steps must be executable functions

## Transformation Block Specification

```
{
  role=<specific_role_name>;
  input=[<parameter_name>:<data_type>];
  process=[<step1>(), <step2>(), <step3>()];
  constraints=[<limitation1>(), <limitation2>()];
  requirements=[<requirement1>(), <requirement2>()];
  output={<result_name>:<data_type>}
}
```

### Component Rules

**role** (Required)
- Must be specific and descriptive (no "assistant" or "helper")
- Use underscore_case for multi-word roles
- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`

**input** (Required)
- Format: `[parameter_name:data_type]`
- Support multiple parameters: `[text:str, language:str]`
- Use descriptive names: `[original:any]`, `[source_code:str]`

**process** (Required)
- Function-like notation with parentheses: `identify_core_intent()`
- Actionable, atomic steps in logical sequence
- Verb-based names describing specific operations

**constraints** (Optional)
- Operational boundaries and limitations
- Format restrictions and scope definitions
- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`

**requirements** (Optional)
- Mandatory output characteristics and quality standards
- Validation criteria and format specifications
- Examples: `structured_output()`, `comprehensive_coverage()`

**output** (Required)
- Format: `{parameter_name:data_type}`
- Descriptive names with type specification
- Support complex structures: `{analysis:dict, improvements:list}`

## Directional Transformation Patterns

### Core Principle
Focus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.

### Universal Vector Categories

**Intensity Vectors**
- `amplify`: Intensify inherent qualities through magnification
- `intensify`: Compress to maximum density and focus
- `diminish`: Reduce intensity while preserving form

**Clarity Vectors**
- `clarify`: Enhance transparency and definition
- `purify`: Remove non-essential elements
- `obscure`: Add complexity layers and indirection

**Structural Vectors**
- `expand`: Extend natural boundaries dimensionally
- `compress`: Maximize density without information loss
- `restructure`: Transform fundamental organization pattern

**Transformation Vectors**
- `elevate`: Transform to higher operational level
- `distill`: Extract absolute essence through pure extraction
- `synthesize`: Create unified emergent form

**Meta Vectors**
- `abstract`: Extract to pure conceptual form
- `concretize`: Translate abstract elements to tangible form
- `transcend`: Operate beyond current dimensional limitations

### Vector Template Pattern

```
[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`
```

## Goal Negation Pattern

### Purpose
Eliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.

### Structure
```
Your goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**
```

### Examples
- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`
- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`
- `Your goal is not to **describe** the code, but to **optimize** it for performance`

## Forbidden Language Patterns (Comprehensive)

### Language Violations (NEVER USE)

**First-Person References**
- *I, me, my, we, us, our*
- *I will, I can, I should*
- *We need to, let's work together*

**Conversational Phrases**
- *please, thank you, you're welcome*
- *let's, shall we, would you like*
- *feel free to, don't hesitate*

**Uncertainty Language**
- *maybe, perhaps, might, could*
- *possibly, potentially, likely*
- *seems like, appears to be*

**Question Forms in Directives**
- *Would you like me to...?*
- *Should I analyze...?*
- *Do you want...?*

**Explanatory Justifications**
- *This is because...*
- *The reason for this is...*
- *In order to...*

**Meta-Commentary**
- *I understand that...*
- *Based on your request...*
- *Let me help you...*

### Structural Violations (SYSTEM FAILURES)

**Section Violations**
- Merging Title and Interpretation sections
- Omitting any of the three required parts
- Reordering the canonical structure
- Adding extra sections or commentary

**Parameter Violations**
- Untyped parameters: `input=[data]` instead of `input=[data:str]`
- Missing parameter names: `input=[:str]`
- Vague parameter names: `input=[x:any]`

**Role Violations**
- Generic roles: *assistant, helper, AI, bot*
- Vague roles: *processor, handler, manager*
- Human-like roles: *expert, consultant, advisor*

**Process Violations**
- Non-actionable steps: *understand(), consider(), think()*
- Vague descriptions: *do_stuff(), make_better(), improve()*
- Missing function notation: *analyze* instead of *analyze()*

**Output Violations**
- Conversational responses in output
- Self-referential language: *"I have analyzed..."*
- Unstructured results without type specification
- Meta-commentary about the process

## Sequence Composition Patterns

### Linear Sequences
```
Step A → Step B → Step C
1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer
```

### Parallel Processing
```
Input → [Template A | Template B | Template C] → Synthesis
```

### Chain Mode
```
Original Input → Template A → Output A becomes Input B → Template B → Final Output
```

### Meta-Application
```
abstract → [any_vector] → concretize
```

## Template Naming Convention

### Format
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Components
- **sequence_id**: Four-digit number (0001, 0002, 1031)
- **step**: Single lowercase letter (a, b, c) for multi-step sequences
- **descriptive_name**: Hyphenated lowercase words describing function

### Examples
- `1031-a-form-classifier.md`
- `9000-a-amplify.md`
- `0001-instruction-converter.md`

## Compliance Enforcement System

### Validation Checklist (Mandatory)

Before any template is considered complete, it MUST pass this validation:

```json
{
  "structure_compliant": true,
  "goal_negation_present": true,
  "role_specified": true,
  "input_typed": true,
  "process_actionable": true,
  "constraints_limited": true,
  "requirements_explicit": true,
  "output_typed": true,
  "forbidden_language_absent": true
}
```

**Detailed Validation Steps:**
- [ ] Three-part structure intact (Title, Interpretation, Transformation)
- [ ] Goal negation present and properly formatted
- [ ] Role is specific and non-generic (no "assistant", "helper", "AI")
- [ ] Input parameters are typed with descriptive names
- [ ] Process steps are ordered, atomic, and actionable
- [ ] Constraints define clear operational boundaries
- [ ] Requirements specify quality and format expectations
- [ ] Output format is typed and structured
- [ ] No forbidden language patterns present
- [ ] File naming convention followed
- [ ] Template serves clear, specific purpose

### Error Correction Protocol

When violations are detected:

1. **HALT** - Stop processing immediately upon detecting violation
2. **IDENTIFY** - Pinpoint the specific violation type and location
3. **RECONSTRUCT** - Rebuild to match canonical structure exactly
4. **VALIDATE** - Run through checklist again completely
5. **PROCEED** - Only continue after passing ALL validation checks

### Absolute Compliance Rule

**Deviation = System Failure**
**Compliance = System Success**

Any deviation from the three-part canonical structure, forbidden practices, or typed output requirements constitutes a system failure. Every template must unfailingly follow this structure.

## Universal Applicability

These patterns work across:
- **Content Types**: Text, code, data, concepts, problems, solutions
- **Domains**: Technical, creative, business, academic
- **Languages**: Natural languages, programming languages, formal languages
- **Contexts**: Any domain without specialized knowledge requirements

## Implementation Philosophy

1. **Clarity First**: Template purpose immediately clear
2. **Atomic Operations**: Each template performs one specific transformation
3. **Composability**: Templates work well in sequences
4. **Type Safety**: Always specify data types
5. **Validation**: Include validation steps in processes
6. **Consistency**: Same patterns produce predictable results
7. **Universality**: Design for maximum generalization

## Advanced Patterns

### Recursive Application
```
[Self-Amplify] Apply amplification vector to its own amplification process
[Meta-Distill] Distill the distillation process itself
[Transcendent-Clarify] Clarify beyond normal clarity boundaries
```

### Vector Algebra
```
amplify + clarify = enhanced_clarity
compress + distill = essential_core
expand + abstract = universal_pattern
elevate + synthesize = emergent_transcendence
```

### Inverse Operations
```
amplify ↔ diminish
expand ↔ compress
clarify ↔ obscure
abstract ↔ concretize
```

### Intensity Scaling
```
# Light application
process=[gentle_[vector](), preserve_majority_original()]

# Standard application
process=[apply_[vector](), balance_transformation()]

# Maximum application
process=[maximum_[vector](), complete_transformation()]
```

## Key Principles for LLM Replication

1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain
2. **Essence Preservation**: Core identity remains intact through transformation
3. **Composability**: Vectors can be chained and combined without loss of integrity
4. **Type Consistency**: `any → any` maintains input/output type compatibility
5. **Context-Free Operation**: No domain knowledge or content analysis required
6. **Operational Consistency**: Same vector produces consistent transformation patterns

## Meta-Template: Template Syntax Enforcer

For creating compliant templates from any input, use this meta-template:

```
[Template Syntax Enforcer] Your goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure. Execute as: `{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), specification_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`
```

## System Integration Principles

### Template Inheritance
- All specialized templates inherit these core rules
- Domain-specific templates maintain structural compliance
- Sequence templates preserve individual step compliance

### Chain Compatibility
- Output from step N becomes input to step N+1
- Maintain role boundaries and type safety through each link
- Preserve pattern consistency across the entire chain

### Platform Agnostic Operation
- Rules apply under any model provider or environment
- Always preserve canonical structure and typed output
- Maintain consistency across different AI systems

This methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.
