# Template Specification: Complete Syntax and Rules

## Core Template Structure

Every template MUST follow this exact format:

```
[Title] Interpretation Execute as: `{Transformation}`
```

This three-part structure is **mandatory** and **non-negotiable** for all templates.

## Part 1: [Title] - Template Identifier

### Format Requirements
- **Enclosure**: Must be enclosed in square brackets `[Title]`
- **Style**: Title case, descriptive, action-oriented
- **Content**: Concise description of template's function
- **Restrictions**: No generic terms like "Template" or "Instruction"

### Examples
```
[Essence Distillation]
[Code Optimizer]
[Content Analyzer]
[Form Classifier]
[Directional Translator]
```

### Invalid Examples
```
[Template]          # Too generic
[Helper]            # Non-specific
[AI Assistant]      # Conversational
[instruction]       # Wrong case
Title               # Missing brackets
```

## Part 2: Interpretation - Human-Readable Instructions

### Goal Negation Pattern (MANDATORY)
Must begin with: `"Your goal is not to **[action]**, but to **[transformation]**"`

### Structure Requirements
- **Command Voice**: No first-person references (I, me, my, we, us)
- **No Conversational Language**: No please, thank you, let's
- **No Uncertainty**: No maybe, perhaps, might, could
- **Clear Directives**: Specific, actionable instructions
- **Ending**: Must end with "Execute as:" leading to transformation block

### Valid Examples
```
Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification.

Your goal is not to **summarize** the content, but to **distill** its absolute essence through pure extraction.

Your goal is not to **describe** the code, but to **optimize** it for readability and performance while maintaining functionality.
```

### Invalid Examples
```
I want you to analyze...                    # First-person
Please help me understand...                # Conversational
You might want to consider...               # Uncertain
Let's work together to...                   # Collaborative
Maybe you could try to...                   # Uncertain
```

## Part 3: `{Transformation}` - Machine-Parsable Parameters

### Format Requirements
- **Enclosure**: Must be enclosed in backticks with curly braces `{...}`
- **Structure**: Semicolon-separated key-value pairs
- **Components**: role, input, process, constraints, requirements, output

### Complete Structure
```
{
  role=<specific_role_name>;
  input=[<parameter_name>:<data_type>];
  process=[<step1>(), <step2>(), <step3>()];
  constraints=[<limitation1>(), <limitation2>()];
  requirements=[<requirement1>(), <requirement2>()];
  output={<result_name>:<data_type>}
}
```

## Component Specifications

### role (Required)
**Purpose**: Defines the functional role of the template

**Format**: `role=<specific_role_name>`

**Rules**:
- Must be specific and descriptive
- No generic roles like "assistant" or "helper"
- Use underscore_case for multi-word roles
- Should reflect the transformation being performed

**Valid Examples**:
```
role=essence_distiller
role=code_optimizer
role=content_analyst
role=form_classifier
role=amplification_operator
```

**Invalid Examples**:
```
role=assistant          # Too generic
role=helper            # Non-specific
role=AI                # Too broad
role=CodeOptimizer     # Wrong case
role=code optimizer    # Spaces not allowed
```

### input (Required)
**Purpose**: Specifies expected input format and parameters

**Format**: `input=[<parameter_name>:<data_type>]`

**Rules**:
- Use descriptive parameter names
- Include data type specification
- Support multiple parameters with comma separation
- Use standard data types: str, int, float, bool, any, list, dict

**Valid Examples**:
```
input=[text:str]
input=[code:str, language:str]
input=[original:any]
input=[content:str, options:dict]
input=[data:list, format:str]
```

**Invalid Examples**:
```
input=[text]           # Missing type
input=[x:str]          # Non-descriptive name
input=text:str         # Missing brackets
input=[text: str]      # Extra space
```

### process (Required)
**Purpose**: Defines ordered processing steps

**Format**: `process=[<step1>(), <step2>(), ...]`

**Rules**:
- Use function-like notation with parentheses
- Steps must be actionable and atomic
- Maintain logical sequence order
- Use descriptive, verb-based names
- Each step should be a specific operation

**Valid Examples**:
```
process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(), validate_essence_preservation()]

process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality()]

process=[parse_structure(), identify_themes(), evaluate_effectiveness(), generate_insights()]
```

**Invalid Examples**:
```
process=[do_stuff(), make_better()]        # Vague operations
process=[step1, step2, step3]              # Missing parentheses
process=[analyze(), improve()]             # Too generic
```

### constraints (Optional)
**Purpose**: Specifies limitations and boundaries

**Format**: `constraints=[<constraint1>(), <constraint2>(), ...]`

**Rules**:
- Define operational boundaries
- Specify format or style limitations
- Include scope restrictions
- Use function-like notation

**Valid Examples**:
```
constraints=[preserve_original_meaning(), maintain_technical_accuracy(), limit_output_length(max_words=100)]

constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]

constraints=[objective_analysis(), evidence_based_conclusions()]
```

### requirements (Optional)
**Purpose**: Defines mandatory output characteristics

**Format**: `requirements=[<requirement1>(), <requirement2>(), ...]`

**Rules**:
- Specify quality standards
- Define format requirements
- Include validation criteria
- Use function-like notation

**Valid Examples**:
```
requirements=[structured_output(), type_safety(), comprehensive_coverage()]

requirements=[improved_readability(), measurable_performance_gains()]

requirements=[actionable_insights(), factual_accuracy()]
```

### output (Required)
**Purpose**: Specifies return format and structure

**Format**: `output={<parameter_name>:<data_type>}`

**Rules**:
- Use descriptive parameter names
- Include data type specification
- Support complex output structures
- Use object notation with curly braces

**Valid Examples**:
```
output={enhanced_prompt:str}
output={analysis:dict}
output={optimized_code:str, improvements:list}
output={distilled_essence:any}
output={classification:str, confidence:float}
```

**Invalid Examples**:
```
output=result:str              # Missing braces
output={result}                # Missing type
output=[result:str]            # Wrong brackets
output={result: str}           # Extra space
```

## Complete Template Examples

### Simple Template
```
[Text Summarizer] Your goal is not to **repeat** the input text, but to **distill** it into its essential points while preserving key information. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary(), validate_completeness()]; constraints=[preserve_critical_information(), maintain_original_tone()]; requirements=[concise_output(), factual_accuracy()]; output={summary:str}}`
```

### Multi-Parameter Template
```
[Code Refactor] Your goal is not to **rewrite** the code arbitrarily, but to **optimize** it for readability and performance while maintaining functionality. Execute as: `{role=code_optimizer; input=[source_code:str, language:str, optimization_goals:list]; process=[analyze_structure(), identify_inefficiencies(), apply_best_practices(), validate_functionality(), generate_improvements()]; constraints=[preserve_behavior(), maintain_compatibility()]; requirements=[improved_readability(), measurable_performance_gains()]; output={refactored_code:str, optimization_report:dict}}`
```

### Directional Vector Template
```
[Amplify] Your goal is not to **analyze** the input, but to **amplify** its inherent qualities through intensification. Execute as: `{role=amplification_operator; input=[content:any]; process=[identify_core_qualities(), intensify_primary_characteristics(), magnify_essential_elements(), enhance_natural_patterns()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={amplified:any}}`
```

## File Naming Convention

### Format
```
<sequence_id>-<step>-<descriptive_name>.md
```

### Components

**sequence_id** (Required)
- Four-digit number with leading zeros
- Groups related templates into sequences
- Examples: `0001`, `0002`, `1031`, `9000`

**step** (Optional)
- Single lowercase letter (a, b, c, d, e, ...)
- Indicates position within a sequence
- Alphabetical order determines execution sequence
- Only used for multi-step sequences

**descriptive_name** (Required)
- Hyphenated lowercase words
- Describes template's specific function
- Concise but descriptive, no spaces
- Examples: `instruction-converter`, `essence-distillation`, `form-classifier`

### Naming Examples

**Standalone Templates**:
```
0001-instruction-converter.md
0005-text-summarizer.md
0010-code-analyzer.md
```

**Sequential Templates**:
```
0002-a-essence-distillation.md
0002-b-coherence-enhancement.md
0002-c-precision-optimization.md
0002-d-structured-transformation.md
```

**Directional Vector Templates**:
```
9000-a-amplify.md
9000-b-intensify.md
9000-c-diminish.md
9001-a-clarify.md
```

## Validation Rules

### Mandatory Compliance Checklist

Every template MUST pass this complete validation before acceptance:

```json
{
  "structure_compliant": true,
  "goal_negation_present": true,
  "role_specified": true,
  "input_typed": true,
  "process_actionable": true,
  "constraints_limited": true,
  "requirements_explicit": true,
  "output_typed": true,
  "forbidden_language_absent": true,
  "naming_convention_followed": true
}
```

### Detailed Validation Steps
- [ ] **Structure**: Three-part structure is intact (Title, Interpretation, Transformation)
- [ ] **Goal Negation**: Present and properly formatted with "Your goal is not to **X**, but to **Y**"
- [ ] **Role**: Specific and non-generic (no "assistant", "helper", "AI")
- [ ] **Input**: Parameters are typed with descriptive names `[name:type]`
- [ ] **Process**: Steps are ordered, atomic, and actionable with function notation
- [ ] **Constraints**: Operational boundaries clearly defined
- [ ] **Requirements**: Quality and format expectations specified
- [ ] **Output**: Format is typed and structured `{name:type}`
- [ ] **Language**: No forbidden language patterns are used
- [ ] **Naming**: File naming convention is followed
- [ ] **Purpose**: Template serves a clear, specific purpose

### Error Correction Protocol

1. **HALT** - Stop immediately upon detecting any validation failure
2. **IDENTIFY** - Pinpoint the specific violation type and location
3. **RECONSTRUCT** - Rebuild the template to match canonical structure
4. **VALIDATE** - Run through complete checklist again
5. **PROCEED** - Only continue after passing ALL validation checks

### Forbidden Practices (Comprehensive)

**Language Violations (NEVER USE)**:

*First-Person References*:
- I, me, my, we, us, our
- I will, I can, I should
- We need to, let's work together

*Conversational Phrases*:
- please, thank you, you're welcome
- let's, shall we, would you like
- feel free to, don't hesitate

*Uncertainty Language*:
- maybe, perhaps, might, could
- possibly, potentially, likely
- seems like, appears to be

*Question Forms*:
- Would you like me to...?
- Should I analyze...?
- Do you want...?

*Explanatory Justifications*:
- This is because...
- The reason for this is...
- In order to...

*Meta-Commentary*:
- I understand that...
- Based on your request...
- Let me help you...

**Structural Violations (SYSTEM FAILURES)**:

*Section Violations*:
- Merging Title and Interpretation sections
- Omitting any of the three required parts
- Reordering the canonical structure
- Adding extra sections or commentary

*Parameter Violations*:
- Untyped parameters: `input=[data]` instead of `input=[data:str]`
- Missing parameter names: `input=[:str]`
- Vague parameter names: `input=[x:any]`

*Role Violations*:
- Generic roles: assistant, helper, AI, bot
- Vague roles: processor, handler, manager
- Human-like roles: expert, consultant, advisor

*Process Violations*:
- Non-actionable steps: understand(), consider(), think()
- Vague descriptions: do_stuff(), make_better(), improve()
- Missing function notation: analyze instead of analyze()

**Output Violations**:
- Conversational responses in output
- Self-referential language: "I have analyzed..."
- Unstructured results without type specification
- Meta-commentary about the process

### Absolute Compliance Rule

**Deviation = Template Rejection**
**Compliance = Template Acceptance**

Any deviation from the three-part canonical structure, forbidden practices, or typed output requirements results in immediate template rejection. Every template must unfailingly follow this specification.

## Integration with Catalog System

Templates are automatically processed by the catalog generation system which:
- Extracts metadata using regex patterns
- Organizes templates into sequences
- Generates searchable catalogs
- Validates template compliance
- Enables dynamic template discovery

For successful integration, templates must strictly adhere to this specification.
