"""
Data structures and models for the AI instruction template system.

This module defines Pydantic models for configuration, execution results,
and other data structures used throughout the system.
"""

from typing import Dict, List, Any, Optional, Callable
from pydantic import BaseModel, Field


class ExecutorConfig(BaseModel):
    """Configuration for sequence execution."""
    
    # Sequence and input parameters
    sequence_steps: List[tuple] = Field(description="Step ID and template data tuples")
    user_prompt: str = Field(description="User input prompt")
    sequence_id: str = Field(description="Sequence identifier")
    models: List[str] = Field(description="Models to use")

    # Output parameters
    output_file: Optional[str] = Field(default=None, description="Output JSON file path")
    minified_output: bool = Field(default=False, description="Minify output")

    # Display options
    show_inputs: bool = Field(default=True, description="Show input prompts")
    show_system_instructions: bool = Field(default=True, description="Show system instructions")
    show_responses: bool = Field(default=True, description="Show responses")

    # Execution options
    chain_mode: bool = Field(default=True, description="Use chain mode")
    aggregator: Optional[str] = Field(default=None, description="Aggregator template ID")
    aggregator_inputs: Optional[List[str]] = Field(default=None, description="Step IDs for aggregation")

    # Aggregator options
    step_offset: int = Field(default=0, description="Step numbering offset")
    is_aggregator: bool = Field(default=False, description="Is aggregator sequence")

    # System components
    system_instruction_extractor: Callable[[Any], str] = Field(description="Instruction extractor")

    # Model parameters
    temperature: Optional[float] = Field(default=None, description="Temperature")
    max_tokens: Optional[int] = Field(default=None, description="Max tokens")

    class Config:
        arbitrary_types_allowed = True


class ModelResponse(BaseModel):
    """Model response data."""
    model: str = Field(description="Model used")
    content: str = Field(description="Response content")


class InstructionResult(BaseModel):
    """Results for one instruction step."""
    instruction: str = Field(description="Instruction used")
    step: str = Field(description="Step identifier")
    title: str = Field(description="Instruction title")
    responses: Dict[str, ModelResponse] = Field(description="Model responses")


class ExecutionResults(BaseModel):
    """Complete sequence execution results."""
    user_prompt: str = Field(description="Initial prompt")
    sequence_id: str = Field(description="Sequence ID")
    results: List[InstructionResult] = Field(description="Step results")


class TemplateMetadata(BaseModel):
    """Metadata for a template."""
    title: str = Field(description="Template title")
    interpretation: str = Field(description="Human-readable interpretation")
    transformation: str = Field(description="Machine-parsable transformation")
    keywords: Optional[str] = Field(default="", description="Extracted keywords")
    raw: str = Field(description="Raw template content")


class SequenceStep(BaseModel):
    """A step in a template sequence."""
    template_id: str = Field(description="Template identifier")
    step: str = Field(description="Step letter (a, b, c, etc.)")
    order: int = Field(description="Execution order")


class CatalogMetadata(BaseModel):
    """Metadata for a template catalog."""
    level: str = Field(description="Template level")
    format: str = Field(description="Template format")
    generated_at: str = Field(description="Generation timestamp")
    source_directory: str = Field(description="Source directory")
    total_templates: int = Field(description="Total number of templates")
    total_sequences: int = Field(description="Total number of sequences")


class TemplateCatalog(BaseModel):
    """Complete template catalog structure."""
    catalog_meta: CatalogMetadata = Field(description="Catalog metadata")
    templates: Dict[str, TemplateMetadata] = Field(description="Template definitions")
    sequences: Dict[str, List[SequenceStep]] = Field(description="Sequence definitions")
