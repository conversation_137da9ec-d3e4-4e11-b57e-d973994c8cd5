# Development Guide

## Project Setup

### Prerequisites

- Python 3.8 or higher
- Git
- Virtual environment tool (venv, conda, etc.)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai-systems
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install in development mode**
   ```bash
   pip install -e .
   ```

### Environment Configuration

Create a `.env` file in the project root with your API keys:

```bash
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GOOGLE_API_KEY=your_google_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here
```

## Project Structure

```
ai-systems/
├── core/                   # Core execution engine
│   ├── __init__.py        # Package initialization
│   ├── __main__.py        # CLI entry point
│   ├── catalog.py         # Template catalog management
│   ├── config.py          # Configuration and settings
│   ├── executor.py        # Main sequence executor
│   ├── models.py          # Data structures
│   └── utils.py           # Utility functions
├── templates/             # Template definitions
│   ├── __init__.py        # Template package
│   ├── lvl1_md_to_json.py # Catalog generator
│   └── md/                # Markdown template files
├── docs/                  # Documentation
├── examples/              # Usage examples
├── requirements.txt       # Python dependencies
├── setup.py              # Package configuration
└── README.md             # Project overview
```

## Development Workflow

### Code Style

This project prioritizes **clarity, simplicity, elegance, precision, and structure**:

- **Readability First**: Write self-explanatory code over excessive commenting
- **Type Hints**: Use type hints for all function parameters and return values
- **Docstrings**: Include docstrings for all modules, classes, and functions
- **Consistent Naming**: Use descriptive names following Python conventions

### Adding New Templates

1. **Create template file** in `templates/md/` following naming convention:
   ```
   <sequence_id>-<step>-<descriptive_name>.md
   ```

2. **Follow three-part structure**:
   ```markdown
   [Title] Interpretation Execute as: `{Transformation}`
   ```

3. **Validate template** using the validation utilities:
   ```python
   from core.utils import ValidationUtils
   result = ValidationUtils.validate_template_structure(template_content)
   ```

4. **Regenerate catalog**:
   ```bash
   python -m core --regenerate-catalog
   ```

### Adding New Features

1. **Create feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement changes** following the established patterns

3. **Add tests** (when test framework is added)

4. **Update documentation** as needed

5. **Submit pull request**

## Testing

### Manual Testing

```bash
# Test basic functionality
python examples/basic_usage.py

# Test CLI interface
python -m core --sequence 1031 --prompt "Test prompt"

# Test template validation
python -c "from core.utils import ValidationUtils; print(ValidationUtils.validate_template_structure('[Test] Test `{role=test}`))"
```

### Template Testing

```bash
# List available sequences
python -m core --list-sequences

# Test specific template
python -m core --show-template 1031-a-form_classifier

# Validate all templates
python -m core --validate-templates
```

## Architecture Guidelines

### Module Organization

- **core/**: Contains the main execution engine and utilities
- **templates/**: Template definitions and catalog management
- **docs/**: All documentation files
- **examples/**: Usage examples and demonstrations

### Design Principles

1. **Separation of Concerns**: Each module has a clear, focused responsibility
2. **Dependency Injection**: Use configuration objects rather than global state
3. **Error Handling**: Graceful degradation with informative error messages
4. **Extensibility**: Design for easy addition of new providers and features

### Data Flow

```
User Input → Prompt Parser → Sequence Manager → Template Catalog → Executor → LLM APIs → Results
```

## Contributing Guidelines

### Code Quality

- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Keep functions focused and small
- Add type hints for all public interfaces
- Write docstrings for all public functions

### Documentation

- Update README.md for user-facing changes
- Update relevant documentation files
- Add examples for new features
- Keep documentation current with code changes

### Template Guidelines

- Follow the exact three-part structure specification
- Use goal negation pattern in interpretation
- Include proper type annotations in transformation blocks
- Test templates with multiple models before submission

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure you're in the project root and virtual environment is activated
2. **API Key Issues**: Check your `.env` file and API key validity
3. **Template Not Found**: Regenerate catalog with `--regenerate-catalog --force`
4. **Model Access**: Verify you have access to the specified models

### Debug Mode

Enable verbose logging:
```bash
python -m core --sequence 1031 --prompt "Test" --verbose
```

### Performance Issues

- Use `--minified` for large outputs
- Limit concurrent model calls
- Check API rate limits

## Release Process

1. **Update version** in `setup.py` and `core/__init__.py`
2. **Update CHANGELOG.md** with new features and fixes
3. **Create release tag**
4. **Build and publish** package (when ready)

## Future Enhancements

- [ ] Add comprehensive test suite
- [ ] Implement template validation CLI
- [ ] Add template performance metrics
- [ ] Create web interface
- [ ] Add template marketplace
- [ ] Implement template versioning

## Overview

This guide provides comprehensive instructions for setting up, developing, and extending the AI Template-Based Instruction Processing System. It covers environment setup, development workflows, testing procedures, and system extension patterns.

## Environment Setup

### Prerequisites
- **Python**: 3.8 or higher
- **Operating System**: Windows, macOS, or Linux
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 1GB free space for dependencies

### Installation Steps

#### 1. Clone and Navigate
```bash
git clone <repository-url>
cd ai_systems.0010--consolidated
```

#### 2. Virtual Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

#### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

#### 4. Environment Configuration
Create a `.env` file in the project root:
```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Configuration
GOOGLE_API_KEY=your_google_api_key_here

# Optional: Custom LiteLLM Configuration
LITELLM_LOG=INFO
```

#### 5. Verify Installation
```bash
python src/lvl1/lvl1_sequence_executor.py --list-models
```

### Development Environment

#### Recommended IDE Setup
- **VS Code**: With Python extension
- **PyCharm**: Professional or Community edition
- **Sublime Text**: With Python packages

#### Essential Extensions/Plugins
- Python syntax highlighting
- JSON formatting
- Markdown preview
- Git integration
- Code linting (pylint, flake8)

## Project Structure

```
ai_systems.0010--consolidated/
├── src/                          # Source code
│   ├── lvl1/                     # Level 1 components
│   │   ├── lvl1_sequence_executor.py    # Main execution engine
│   │   └── templates/            # Template processing
│   │       ├── lvl1_md_to_json.py       # Catalog generator
│   │       └── lvl1.md.generate.extractors.py  # Template generator
│   └── README.md                 # Source documentation
├── venv/                         # Virtual environment
├── requirements.txt              # Python dependencies
├── py_venv_init.bat             # Windows setup script
├── README.md                     # Legacy documentation
├── RulesForAI.md                # Template rules (legacy)
├── RulesForAI.minified.md       # Minified rules (legacy)
├── SYSTEM_OVERVIEW.md           # System overview (new)
├── TEMPLATE_SPECIFICATION.md    # Template spec (new)
├── EXECUTION_ENGINE.md          # Engine docs (new)
└── DEVELOPMENT_GUIDE.md         # This file (new)
```

## Development Workflows

### Template Development

#### 1. Create New Template
```bash
# Navigate to template directory
cd src/lvl1/templates

# Create template file following naming convention
# Format: <sequence_id>-<step>-<descriptive_name>.md
touch 0050-a-new-template.md
```

#### 2. Template Content Structure
```markdown
[Template Title] Your goal is not to **old_action** the input, but to **new_action** it according to specific parameters. Execute as: `{role=specific_role; input=[input_param:type]; process=[step1(), step2(), step3()]; constraints=[constraint1(), constraint2()]; requirements=[requirement1(), requirement2()]; output={result:type}}`
```

#### 3. Validate Template
```bash
# Test template parsing
python lvl1_md_to_json.py --api-test

# Test template execution
python ../lvl1_sequence_executor.py --sequence 0050 "Test input"
```

#### 4. Update Catalog
```bash
# Regenerate catalog to include new template
python lvl1_md_to_json.py
```

### Sequence Development

#### 1. Plan Sequence Steps
- Define overall transformation goal
- Break down into atomic steps
- Ensure logical flow between steps
- Consider input/output compatibility

#### 2. Create Sequence Templates
```bash
# Create sequence with multiple steps
touch 0051-a-step-one.md
touch 0051-b-step-two.md
touch 0051-c-step-three.md
```

#### 3. Test Sequence Flow
```bash
# Test individual steps
python ../lvl1_sequence_executor.py --sequence 0051:a "Test input"

# Test complete sequence
python ../lvl1_sequence_executor.py --sequence 0051 --chain-mode "Test input"

# Test step range
python ../lvl1_sequence_executor.py --sequence 0051:a-b "Test input"
```

### Code Development

#### 1. Core Components Extension

**Adding New Model Support:**
```python
# In lvl1_sequence_executor.py, update MODEL_MAPPING
MODEL_MAPPING = {
    # Existing mappings...
    "new-model-name": "actual-api-model-id"
}

# Update get_available_models() function
def get_available_models():
    return {
        # Existing models...
        "new-provider": ["new-model-name"]
    }
```

**Adding New Template Formats:**
```python
# Create new config class in lvl1_md_to_json.py
class TemplateConfigNewFormat(TemplateConfig):
    LEVEL = "lvl1"
    FORMAT = "new_format"
    SOURCE_DIR = "lvl1/new_format"
    
    PATTERNS = {
        # Define extraction patterns
    }
```

#### 2. Testing Framework

**Unit Tests:**
```python
# tests/test_template_parsing.py
import unittest
from src.lvl1.templates.lvl1_md_to_json import extract_metadata

class TestTemplateParsing(unittest.TestCase):
    def test_valid_template_parsing(self):
        content = "[Test] Your goal is not to **fail**, but to **succeed**. Execute as: `{role=tester; input=[data:str]; process=[validate()]; output={result:bool}}`"
        result = extract_metadata(content, "test-template", TemplateConfigMD())
        self.assertIn("title", result["parts"])
        self.assertEqual(result["parts"]["title"], "Test")
```

**Integration Tests:**
```python
# tests/test_execution.py
import asyncio
import unittest
from src.lvl1.lvl1_sequence_executor import execute_sequence, ExecutorConfig

class TestExecution(unittest.TestCase):
    def test_simple_execution(self):
        config = ExecutorConfig(
            sequence_steps=[("a", {"raw": "Test template"})],
            user_prompt="Test input",
            models=["gpt-3.5-turbo"],
            system_instruction_extractor=lambda x: x.get("raw", "")
        )
        # Test execution logic
```

#### 3. Performance Optimization

**Profiling:**
```python
import cProfile
import pstats

# Profile execution
cProfile.run('execute_sequence(config)', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

**Memory Monitoring:**
```python
import tracemalloc

tracemalloc.start()
# Execute code
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")
```

## Testing Procedures

### Manual Testing

#### 1. Template Validation
```bash
# Test template parsing
python src/lvl1/templates/lvl1_md_to_json.py --api-test

# Validate specific template format
python src/lvl1/lvl1_sequence_executor.py --sequence 0001 --validate-only
```

#### 2. Execution Testing
```bash
# Test single template
python src/lvl1/lvl1_sequence_executor.py --sequence 0001 "Test input"

# Test sequence with multiple models
python src/lvl1/lvl1_sequence_executor.py \
  --sequence 0002 \
  --models "gpt-3.5-turbo,gpt-4" \
  "Test input"

# Test chain mode
python src/lvl1/lvl1_sequence_executor.py \
  --sequence 0002 \
  --chain-mode \
  "Test input"
```

#### 3. Output Validation
```bash
# Test JSON output format
python src/lvl1/lvl1_sequence_executor.py \
  --sequence 0001 \
  --output-file test_output.json \
  "Test input"

# Validate JSON structure
python -m json.tool test_output.json
```

### Automated Testing

#### 1. Setup Test Environment
```bash
# Create test configuration
export OPENAI_API_KEY="test-key"
export TEST_MODE="true"

# Run tests
python -m pytest tests/ -v
```

#### 2. Continuous Integration
```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run tests
        run: python -m pytest tests/
```

## Extension Patterns

### Adding New Functionality

#### 1. New Template Processors
```python
# src/lvl1/templates/custom_processor.py
class CustomTemplateProcessor:
    def __init__(self, config):
        self.config = config
    
    def process_template(self, content):
        # Custom processing logic
        return processed_content
    
    def validate_template(self, template):
        # Custom validation logic
        return is_valid
```

#### 2. Custom Output Formats
```python
# src/lvl1/output/custom_writer.py
class CustomOutputWriter:
    def __init__(self, output_path):
        self.output_path = output_path
    
    def write_results(self, results):
        # Custom output formatting
        pass
```

#### 3. New Execution Modes
```python
# src/lvl1/execution/custom_executor.py
class CustomExecutor:
    def __init__(self, config):
        self.config = config
    
    async def execute(self, sequence_steps, user_prompt):
        # Custom execution logic
        return results
```

### Plugin Architecture

#### 1. Plugin Interface
```python
# src/lvl1/plugins/base.py
from abc import ABC, abstractmethod

class PluginBase(ABC):
    @abstractmethod
    def initialize(self, config):
        pass
    
    @abstractmethod
    def process(self, data):
        pass
```

#### 2. Plugin Registration
```python
# src/lvl1/plugins/registry.py
class PluginRegistry:
    _plugins = {}
    
    @classmethod
    def register(cls, name, plugin_class):
        cls._plugins[name] = plugin_class
    
    @classmethod
    def get_plugin(cls, name):
        return cls._plugins.get(name)
```

## Best Practices

### Code Quality

#### 1. Style Guidelines
- Follow PEP 8 for Python code style
- Use type hints for function parameters and returns
- Write descriptive docstrings for all functions and classes
- Keep functions focused and under 50 lines when possible

#### 2. Error Handling
```python
# Proper error handling pattern
try:
    result = risky_operation()
except SpecificException as e:
    logger.error(f"Operation failed: {e}")
    return default_value
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise
```

#### 3. Logging
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# Use appropriate log levels
logger.debug("Detailed debugging information")
logger.info("General information")
logger.warning("Warning message")
logger.error("Error occurred")
```

### Performance Guidelines

#### 1. Async/Await Usage
```python
# Proper async pattern
async def process_multiple_items(items):
    tasks = [process_item(item) for item in items]
    results = await asyncio.gather(*tasks)
    return results
```

#### 2. Memory Management
```python
# Use generators for large datasets
def process_large_dataset(data):
    for item in data:
        yield process_item(item)

# Clean up resources
try:
    with open(file_path, 'r') as f:
        content = f.read()
finally:
    # Cleanup code
    pass
```

### Security Considerations

#### 1. API Key Management
- Never commit API keys to version control
- Use environment variables or secure key management
- Implement key rotation procedures
- Monitor API usage for anomalies

#### 2. Input Validation
```python
def validate_input(user_input):
    # Sanitize and validate user input
    if not isinstance(user_input, str):
        raise ValueError("Input must be string")
    
    if len(user_input) > MAX_INPUT_LENGTH:
        raise ValueError("Input too long")
    
    return sanitize_string(user_input)
```

## Troubleshooting

### Common Development Issues

#### 1. Import Errors
```bash
# Fix Python path issues
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# Or use relative imports
from ..templates import lvl1_md_to_json
```

#### 2. Template Parsing Issues
- Verify template follows exact three-part format
- Check for proper escaping of special characters
- Validate JSON-like transformation block syntax

#### 3. Execution Failures
- Verify API keys are correctly set
- Check network connectivity
- Validate model names and availability
- Review rate limiting and quota issues

### Debug Tools

#### 1. Template Debugging
```python
# Debug template parsing
import re
from src.lvl1.templates.lvl1_md_to_json import TemplateConfigMD

config = TemplateConfigMD()
pattern = config.PATTERNS["title"]["pattern"]
match = pattern.search(template_content)
if match:
    print(f"Title: {match.group(1)}")
else:
    print("No match found")
```

#### 2. Execution Debugging
```bash
# Enable verbose logging
export LITELLM_LOG=DEBUG
python src/lvl1/lvl1_sequence_executor.py --sequence 0001 "Debug input"
```

## Contributing Guidelines

### Code Contributions

#### 1. Development Process
1. Fork the repository
2. Create feature branch: `git checkout -b feature/new-feature`
3. Make changes following style guidelines
4. Add tests for new functionality
5. Update documentation
6. Submit pull request

#### 2. Commit Messages
```
feat: add new template validation feature
fix: resolve catalog generation issue
docs: update template specification
test: add unit tests for sequence resolution
```

### Documentation Contributions

#### 1. Template Documentation
- Include clear examples for new templates
- Document any special requirements or constraints
- Provide usage examples and expected outputs

#### 2. Code Documentation
- Update docstrings for modified functions
- Add inline comments for complex logic
- Update README files for structural changes

### Review Process

#### 1. Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes without migration path
- [ ] Performance impact is acceptable

#### 2. Template Review Checklist
- [ ] Follows three-part structure specification
- [ ] Uses proper goal negation pattern
- [ ] Has typed parameters and outputs
- [ ] Includes actionable process steps
- [ ] Serves clear, specific purpose
