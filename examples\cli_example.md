# CLI Usage Examples

This document provides examples of how to use the AI Systems template framework from the command line.

## Basic Usage

### Execute a Single Template Sequence

```bash
# Execute sequence 1031 with default model
python -m core --sequence 1031 --prompt "Analyze this code structure"

# Execute with specific model
python -m core --sequence 1031 --models gpt-4o --prompt "Analyze this code structure"

# Execute with multiple models
python -m core --sequence 1031 --models gpt-4o claude-3-sonnet --prompt "Analyze this code structure"
```

### Execute Multi-Step Sequences

```bash
# Execute steps a through c of sequence 9000
python -m core --sequence 9000:a-c --prompt "Transform this content"

# Execute multiple sequences
python -m core --sequence "1031|9000" --prompt "Process this input"

# Complex sequence specification
python -m core --sequence "1031:a-c|9000:a-b" --prompt "Multi-stage processing"
```

### Output Options

```bash
# Save output to specific file
python -m core --sequence 1031 --prompt "Test" --output results.json

# Minified output
python -m core --sequence 1031 --prompt "Test" --minified

# Custom output directory
python -m core --sequence 1031 --prompt "Test" --output-dir ./results/
```

### Display Options

```bash
# Hide system instructions
python -m core --sequence 1031 --prompt "Test" --no-show-instructions

# Hide responses (useful for testing)
python -m core --sequence 1031 --prompt "Test" --no-show-responses

# Show only final results
python -m core --sequence 1031 --prompt "Test" --no-show-inputs --no-show-instructions
```

### Chain Mode

```bash
# Enable chain mode (output of step N becomes input of step N+1)
python -m core --sequence 9000:a-c --prompt "Initial input" --chain

# Disable chain mode (all steps use original prompt)
python -m core --sequence 9000:a-c --prompt "Initial input" --no-chain
```

## Advanced Usage

### Model Configuration

```bash
# Use specific provider's default model
python -m core --sequence 1031 --provider anthropic --prompt "Test"

# Override model parameters
python -m core --sequence 1031 --models gpt-4o --temperature 0.7 --max-tokens 1000 --prompt "Test"
```

### Template Management

```bash
# Regenerate template catalog
python -m core --regenerate-catalog

# Force regeneration even if up to date
python -m core --regenerate-catalog --force

# List available sequences
python -m core --list-sequences

# Show template details
python -m core --show-template 1031-a-form_classifier
```

### Embedded Sequence Specifications

You can embed sequence specifications directly in your prompts:

```bash
# Using [SEQ:...] syntax
python -m core --prompt "[SEQ:1031:a-c] Analyze this code structure"

# Using --seq= syntax  
python -m core --prompt "Analyze this code structure --seq=1031:a-c"
```

## Common Patterns

### Content Analysis Pipeline

```bash
# Multi-step content analysis
python -m core --sequence "1031:a-d|9001:a-b" --prompt "$(cat document.txt)" --chain
```

### Code Processing

```bash
# Code analysis and optimization
python -m core --sequence "1020:a-d" --prompt "$(cat script.py)" --models gpt-4o
```

### Prompt Engineering

```bash
# Test prompt variations
python -m core --sequence "9000:a-c" --models gpt-4o claude-3-sonnet gemini-2.5-pro --prompt "Original prompt"
```

## Environment Variables

You can set environment variables to configure default behavior:

```bash
# Set default model
export AI_SYSTEMS_DEFAULT_MODEL=gpt-4o

# Set default output directory
export AI_SYSTEMS_OUTPUT_DIR=./results/

# Set API keys
export OPENAI_API_KEY=your_key_here
export ANTHROPIC_API_KEY=your_key_here
export GOOGLE_API_KEY=your_key_here
```

## Troubleshooting

### Common Issues

1. **Template not found**: Use `--list-sequences` to see available sequences
2. **Model not available**: Check your API keys and model access
3. **Output directory errors**: Ensure the directory exists or use `--create-dirs`

### Debug Mode

```bash
# Enable verbose output
python -m core --sequence 1031 --prompt "Test" --verbose

# Show catalog information
python -m core --show-catalog-info
```
