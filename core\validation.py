"""
Template validation system for AI instruction templates.

This module provides comprehensive validation based on the RulesForAI specification,
ensuring all templates comply with the canonical three-part structure and forbidden
language patterns.
"""

import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass


@dataclass
class ValidationResult:
    """Result of template validation."""
    is_valid: bool
    score: float  # 0.0 to 1.0
    violations: List[str]
    warnings: List[str]
    compliance_checklist: Dict[str, bool]


class TemplateValidator:
    """Comprehensive template validator based on RulesForAI specification."""
    
    # Forbidden language patterns
    FORBIDDEN_FIRST_PERSON = [
        r'\bI\b', r'\bme\b', r'\bmy\b', r'\bwe\b', r'\bus\b', r'\bour\b',
        r'\bI will\b', r'\bI can\b', r'\bI should\b', r'\bwe need to\b'
    ]
    
    FORBIDDEN_CONVERSATIONAL = [
        r'\bplease\b', r'\bthank you\b', r'\byou\'re welcome\b',
        r'\blet\'s\b', r'\bshall we\b', r'\bwould you like\b',
        r'\bfeel free to\b', r'\bdon\'t hesitate\b'
    ]
    
    FORBIDDEN_UNCERTAINTY = [
        r'\bmaybe\b', r'\bperhaps\b', r'\bmight\b', r'\bcould\b',
        r'\bpossibly\b', r'\bpotentially\b', r'\blikely\b',
        r'\bseems like\b', r'\bappears to be\b'
    ]
    
    FORBIDDEN_QUESTIONS = [
        r'Would you like me to\?', r'Should I\?', r'Do you want\?',
        r'Can I\?', r'May I\?'
    ]
    
    FORBIDDEN_META = [
        r'This is because', r'The reason for this is', r'In order to',
        r'I understand that', r'Based on your request', r'Let me help you'
    ]
    
    GENERIC_ROLES = [
        'assistant', 'helper', 'ai', 'bot', 'processor', 'handler', 
        'manager', 'expert', 'consultant', 'advisor'
    ]
    
    def __init__(self):
        """Initialize the validator with compiled patterns."""
        self.forbidden_patterns = []
        
        # Compile all forbidden patterns
        for pattern_list in [
            self.FORBIDDEN_FIRST_PERSON,
            self.FORBIDDEN_CONVERSATIONAL, 
            self.FORBIDDEN_UNCERTAINTY,
            self.FORBIDDEN_QUESTIONS,
            self.FORBIDDEN_META
        ]:
            self.forbidden_patterns.extend([re.compile(p, re.IGNORECASE) for p in pattern_list])
    
    def validate_template(self, template_content: str) -> ValidationResult:
        """
        Perform comprehensive validation of a template.
        
        Args:
            template_content: Raw template content to validate
            
        Returns:
            ValidationResult with detailed compliance information
        """
        violations = []
        warnings = []
        checklist = {}
        
        # Parse template structure
        structure = self._parse_structure(template_content)
        
        # Validate three-part structure
        checklist["structure_compliant"] = self._validate_structure(structure, violations)
        
        # Validate goal negation
        checklist["goal_negation_present"] = self._validate_goal_negation(
            structure.get("interpretation", ""), violations
        )
        
        # Validate transformation block
        transformation = structure.get("transformation", "")
        checklist["role_specified"] = self._validate_role(transformation, violations)
        checklist["input_typed"] = self._validate_input(transformation, violations)
        checklist["process_actionable"] = self._validate_process(transformation, violations)
        checklist["constraints_limited"] = self._validate_constraints(transformation, warnings)
        checklist["requirements_explicit"] = self._validate_requirements(transformation, warnings)
        checklist["output_typed"] = self._validate_output(transformation, violations)
        
        # Validate forbidden language
        checklist["forbidden_language_absent"] = self._validate_language(
            template_content, violations
        )
        
        # Calculate overall compliance
        compliance_score = sum(checklist.values()) / len(checklist)
        is_valid = len(violations) == 0
        
        return ValidationResult(
            is_valid=is_valid,
            score=compliance_score,
            violations=violations,
            warnings=warnings,
            compliance_checklist=checklist
        )
    
    def _parse_structure(self, content: str) -> Dict[str, str]:
        """Parse template into three parts."""
        # Pattern for three-part structure
        pattern = re.compile(
            r"\[(.*?)\]"     # Group 1: Title
            r"\s*"           # Whitespace
            r"(.*?)"         # Group 2: Interpretation
            r"\s*"           # Whitespace
            r"(`\{.*?\}`)"   # Group 3: Transformation
        )
        
        match = pattern.search(content.strip())
        
        if match:
            return {
                "title": match.group(1).strip(),
                "interpretation": match.group(2).strip(),
                "transformation": match.group(3).strip()
            }
        
        return {"title": "", "interpretation": "", "transformation": ""}
    
    def _validate_structure(self, structure: Dict[str, str], violations: List[str]) -> bool:
        """Validate three-part structure is present."""
        if not structure["title"]:
            violations.append("Missing title section in square brackets [Title]")
            return False
        
        if not structure["interpretation"]:
            violations.append("Missing interpretation section")
            return False
        
        if not structure["transformation"]:
            violations.append("Missing transformation block `{...}`")
            return False
        
        return True
    
    def _validate_goal_negation(self, interpretation: str, violations: List[str]) -> bool:
        """Validate goal negation pattern."""
        pattern = r"Your goal is not to \*\*(.*?)\*\*, but to \*\*(.*?)\*\*"
        
        if not re.search(pattern, interpretation):
            violations.append("Missing goal negation pattern: 'Your goal is not to **X**, but to **Y**'")
            return False
        
        return True
    
    def _validate_role(self, transformation: str, violations: List[str]) -> bool:
        """Validate role specification."""
        role_pattern = r"role=([^;]+)"
        match = re.search(role_pattern, transformation)
        
        if not match:
            violations.append("Missing role specification in transformation block")
            return False
        
        role = match.group(1).strip()
        
        # Check for generic roles
        for generic in self.GENERIC_ROLES:
            if generic.lower() in role.lower():
                violations.append(f"Generic role '{role}' not allowed. Use specific role name.")
                return False
        
        return True
    
    def _validate_input(self, transformation: str, violations: List[str]) -> bool:
        """Validate input parameter typing."""
        input_pattern = r"input=\[([^\]]+)\]"
        match = re.search(input_pattern, transformation)
        
        if not match:
            violations.append("Missing input specification: input=[name:type]")
            return False
        
        input_spec = match.group(1)
        
        # Check for type specification
        if ":" not in input_spec:
            violations.append("Input parameters must be typed: [name:type]")
            return False
        
        return True
    
    def _validate_process(self, transformation: str, violations: List[str]) -> bool:
        """Validate process steps."""
        process_pattern = r"process=\[([^\]]+)\]"
        match = re.search(process_pattern, transformation)
        
        if not match:
            violations.append("Missing process specification: process=[step1(), step2()]")
            return False
        
        process_spec = match.group(1)
        
        # Check for function notation
        if "()" not in process_spec:
            violations.append("Process steps must use function notation: step()")
            return False
        
        return True
    
    def _validate_constraints(self, transformation: str, warnings: List[str]) -> bool:
        """Validate constraints (optional but recommended)."""
        if "constraints=" not in transformation:
            warnings.append("Consider adding constraints to define operational boundaries")
            return False
        return True
    
    def _validate_requirements(self, transformation: str, warnings: List[str]) -> bool:
        """Validate requirements (optional but recommended)."""
        if "requirements=" not in transformation:
            warnings.append("Consider adding requirements to specify quality expectations")
            return False
        return True
    
    def _validate_output(self, transformation: str, violations: List[str]) -> bool:
        """Validate output specification."""
        output_pattern = r"output=\{([^}]+)\}"
        match = re.search(output_pattern, transformation)
        
        if not match:
            violations.append("Missing output specification: output={name:type}")
            return False
        
        output_spec = match.group(1)
        
        # Check for type specification
        if ":" not in output_spec:
            violations.append("Output must be typed: {name:type}")
            return False
        
        return True
    
    def _validate_language(self, content: str, violations: List[str]) -> bool:
        """Validate against forbidden language patterns."""
        found_violations = []
        
        for pattern in self.forbidden_patterns:
            matches = pattern.findall(content)
            if matches:
                found_violations.extend(matches)
        
        if found_violations:
            violations.append(f"Forbidden language detected: {', '.join(set(found_violations))}")
            return False
        
        return True


def validate_template_file(file_path: str) -> ValidationResult:
    """
    Validate a template file.
    
    Args:
        file_path: Path to template file
        
    Returns:
        ValidationResult with compliance information
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        validator = TemplateValidator()
        return validator.validate_template(content)
    
    except Exception as e:
        return ValidationResult(
            is_valid=False,
            score=0.0,
            violations=[f"Error reading file: {str(e)}"],
            warnings=[],
            compliance_checklist={}
        )


def print_validation_report(result: ValidationResult, template_name: str = "Template"):
    """Print a formatted validation report."""
    print(f"\n=== Validation Report: {template_name} ===")
    print(f"Valid: {'✅ YES' if result.is_valid else '❌ NO'}")
    print(f"Compliance Score: {result.score:.1%}")
    
    print(f"\nCompliance Checklist:")
    for check, passed in result.compliance_checklist.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {check}")
    
    if result.violations:
        print(f"\n❌ Violations ({len(result.violations)}):")
        for violation in result.violations:
            print(f"  • {violation}")
    
    if result.warnings:
        print(f"\n⚠️  Warnings ({len(result.warnings)}):")
        for warning in result.warnings:
            print(f"  • {warning}")
    
    print("=" * 50)
