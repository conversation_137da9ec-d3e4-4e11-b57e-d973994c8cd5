"""
Template validation example script.

This script demonstrates how to use the validation system to check
template compliance with the RulesForAI specification.
"""

import sys
import os
import glob

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.validation import TemplateValidator, validate_template_file, print_validation_report


def validate_single_template():
    """Example: Validate a single template string."""
    print("=== Single Template Validation Example ===")
    
    # Example of a compliant template
    compliant_template = """
[Data Processor] Your goal is not to **analyze** the input, but to **transform** it into a structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`
"""
    
    # Example of a non-compliant template
    non_compliant_template = """
[Helper] I will help you analyze the input data. Please provide the data and I'll do my best to process it for you. Execute as: `{role=assistant; input=[data]; process=[analyze, improve]; output=result}`
"""
    
    validator = TemplateValidator()
    
    print("\n--- Compliant Template ---")
    result1 = validator.validate_template(compliant_template)
    print_validation_report(result1, "Compliant Example")
    
    print("\n--- Non-Compliant Template ---")
    result2 = validator.validate_template(non_compliant_template)
    print_validation_report(result2, "Non-Compliant Example")


def validate_template_files():
    """Example: Validate template files from the templates directory."""
    print("\n=== Template File Validation Example ===")
    
    # Look for template files
    template_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "templates", "md")
    
    if not os.path.exists(template_dir):
        print(f"Template directory not found: {template_dir}")
        return
    
    # Find all .md files
    template_files = glob.glob(os.path.join(template_dir, "*.md"))
    
    if not template_files:
        print(f"No template files found in: {template_dir}")
        return
    
    print(f"Found {len(template_files)} template files")
    
    # Validate each file
    total_files = 0
    valid_files = 0
    
    for file_path in template_files[:5]:  # Limit to first 5 for example
        total_files += 1
        file_name = os.path.basename(file_path)
        
        print(f"\n--- Validating: {file_name} ---")
        result = validate_template_file(file_path)
        
        if result.is_valid:
            valid_files += 1
            print(f"✅ {file_name}: VALID (Score: {result.score:.1%})")
        else:
            print(f"❌ {file_name}: INVALID (Score: {result.score:.1%})")
            print(f"   Violations: {len(result.violations)}")
            for violation in result.violations[:3]:  # Show first 3 violations
                print(f"   • {violation}")
    
    print(f"\n=== Summary ===")
    print(f"Total files checked: {total_files}")
    print(f"Valid files: {valid_files}")
    print(f"Invalid files: {total_files - valid_files}")
    print(f"Overall compliance: {valid_files/total_files:.1%}")


def demonstrate_validation_features():
    """Demonstrate specific validation features."""
    print("\n=== Validation Features Demonstration ===")
    
    validator = TemplateValidator()
    
    # Test cases for different violation types
    test_cases = [
        {
            "name": "Missing Goal Negation",
            "template": "[Test] Transform the input. Execute as: `{role=transformer; input=[data:str]; process=[transform()]; output={result:str}}`"
        },
        {
            "name": "Generic Role",
            "template": "[Test] Your goal is not to **analyze**, but to **transform**. Execute as: `{role=assistant; input=[data:str]; process=[transform()]; output={result:str}}`"
        },
        {
            "name": "Untyped Parameters",
            "template": "[Test] Your goal is not to **analyze**, but to **transform**. Execute as: `{role=transformer; input=[data]; process=[transform()]; output={result}}`"
        },
        {
            "name": "Forbidden Language",
            "template": "[Test] Your goal is not to **analyze**, but to **transform**. I will help you process this data. Execute as: `{role=transformer; input=[data:str]; process=[transform()]; output={result:str}}`"
        },
        {
            "name": "Missing Process Functions",
            "template": "[Test] Your goal is not to **analyze**, but to **transform**. Execute as: `{role=transformer; input=[data:str]; process=[transform]; output={result:str}}`"
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        result = validator.validate_template(test_case['template'])
        
        print(f"Valid: {'✅' if result.is_valid else '❌'}")
        print(f"Score: {result.score:.1%}")
        
        if result.violations:
            print("Violations:")
            for violation in result.violations:
                print(f"  • {violation}")


def create_compliant_template_example():
    """Show how to create a compliant template step by step."""
    print("\n=== Creating a Compliant Template ===")
    
    print("Step 1: Start with the three-part structure")
    print("[Title] Interpretation Execute as: `{Transformation}`")
    
    print("\nStep 2: Add specific title")
    print("[Content Summarizer] ...")
    
    print("\nStep 3: Add goal negation and interpretation")
    print("[Content Summarizer] Your goal is not to **repeat** the content, but to **distill** it into essential points. Execute as: ...")
    
    print("\nStep 4: Add transformation block with all components")
    final_template = """[Content Summarizer] Your goal is not to **repeat** the content, but to **distill** it into essential points while preserving key information. Execute as: `{role=content_summarizer; input=[text:str]; process=[identify_main_points(), extract_key_details(), synthesize_summary(), validate_completeness()]; constraints=[preserve_critical_information(), maintain_original_tone()]; requirements=[concise_output(), factual_accuracy()]; output={summary:str}}`"""
    
    print(f"\nFinal template:\n{final_template}")
    
    print("\nStep 5: Validate the template")
    validator = TemplateValidator()
    result = validator.validate_template(final_template)
    print_validation_report(result, "Created Template")


def main():
    """Run all validation examples."""
    print("AI Systems Template Validation Examples")
    print("=" * 50)
    
    try:
        validate_single_template()
        validate_template_files()
        demonstrate_validation_features()
        create_compliant_template_example()
        
        print("\n" + "=" * 50)
        print("Validation examples completed successfully!")
        
    except Exception as e:
        print(f"Error running validation examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
