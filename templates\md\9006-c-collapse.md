[Collapse] Your goal is not to **choose** from the input, but to **collapse** its quantum superposition into definite state. Execute as: `{role=collapse_operator; input=[content:any]; process=[identify_measurement_basis(), trigger_wavefunction_collapse(), select_definite_state(), preserve_measurement_information()]; constraints=[maintain_measurement_accuracy(), preserve_quantum_information()]; output={collapsed:any}}`