# AI Systems: Template-Based Instruction Processing

A sophisticated framework for creating, managing, and executing structured AI instructions through a template-based approach. This system enables advanced prompt engineering workflows by organizing AI instructions into reusable, composable templates that can be executed in sequences across multiple LLM models.

## 🎯 Core Philosophy

The system is built around **structured instruction processing** where every AI interaction follows a standardized three-part template format:

1. **[Title]** - Defines the template's purpose
2. **Interpretation** - Human-readable explanation with goal negation pattern  
3. **`{Transformation}`** - Machine-parsable execution parameters

This approach ensures consistency, reusability, and optimal LLM performance while eliminating ambiguity in AI instructions.

## 🏗️ Architecture Overview

```
AI Template System
├── 📁 templates/           # Template definitions and catalogs
│   ├── md/                # Markdown template files
│   └── generators/        # Catalog generation tools
├── 🔧 core/               # Core execution engine
│   ├── executor.py        # Main sequence executor
│   ├── config.py          # Configuration management
│   └── models.py          # Data structures
├── 📚 docs/               # Documentation
└── 🧪 examples/           # Usage examples
```

## ✨ Key Features

### Template Management
- **Structured Format**: Standardized three-part template structure
- **Automatic Cataloging**: Dynamic template discovery and organization
- **Sequence Support**: Multi-step workflows with dependency management
- **Metadata Extraction**: Automatic keyword and role identification

### Execution Engine
- **Multi-Model Support**: OpenAI, Anthropic, Google, and custom providers via LiteLLM
- **Asynchronous Processing**: Concurrent execution with streaming output
- **Cost Tracking**: Real-time token usage and cost monitoring
- **Chain Mode**: Sequential processing where output becomes next input
- **Flexible Output**: JSON, streaming, and minified formats

### Quality Assurance
- **Template Validation**: Compliance checking against specification
- **Type Safety**: Structured input/output with data type enforcement
- **Error Handling**: Graceful degradation and fallback mechanisms
- **Performance Monitoring**: Execution metrics and optimization insights

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd ai-systems

# Set up Python environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```bash
# Execute a single template
python -m core.executor --sequence 1031 --prompt "Analyze this code structure"

# Run a multi-step sequence
python -m core.executor --sequence 9000:a-c --prompt "Transform this content"

# Use specific models
python -m core.executor --sequence 1010 --models gpt-4o claude-3-sonnet --prompt "Your input"
```

### Template Creation

Create a new template following the three-part structure:

```markdown
[Content Analyzer] Your goal is not to **describe** the content superficially, but to **analyze** its structure, themes, and effectiveness systematically. Execute as: `{role=content_analyst; input=[content:any]; process=[parse_structure(), identify_themes(), evaluate_effectiveness(), assess_clarity(), generate_insights()]; constraints=[objective_analysis(), evidence_based_conclusions()]; requirements=[comprehensive_coverage(), actionable_insights()]; output={analysis:dict}}`
```

## 📖 Documentation

The documentation is organized in a natural sequential order for progressive understanding:

- **[01_INSTRUCTION_PATTERNS](docs/01_INSTRUCTION_PATTERNS.md)** - Complete guide to generalized instruction patterns
- **[02_DIRECTIONAL_VECTORS](docs/02_DIRECTIONAL_VECTORS.md)** - Complete theory and implementation of directional vectors
- **[03_TEMPLATE_SPECIFICATION](docs/03_TEMPLATE_SPECIFICATION.md)** - Complete template syntax and rules
- **[04_SYSTEM_ARCHITECTURE](docs/04_SYSTEM_ARCHITECTURE.md)** - How everything ties together technically
- **[05_EXECUTION_GUIDE](docs/05_EXECUTION_GUIDE.md)** - How to use the system (CLI + programmatic)
- **[06_DEVELOPMENT_GUIDE](docs/06_DEVELOPMENT_GUIDE.md)** - How to extend and develop the system
- **[07_QUICK_REFERENCE](docs/07_QUICK_REFERENCE.md)** - Condensed reference for all concepts

Each document is self-contained and can be used independently as context in other LLM conversations.

## 🎯 Use Cases

- **Prompt Engineering**: Iterative refinement and A/B testing of AI instructions
- **Content Processing**: Multi-step document analysis and transformation pipelines
- **AI Research**: Template effectiveness comparison and model behavior analysis
- **Production Systems**: Standardized AI instruction deployment and management

## 🤝 Contributing

This project prioritizes clarity, simplicity, elegance, precision, and structure. When contributing:

- Follow the established template specification exactly
- Maintain consistent coding style with emphasis on readability
- Write self-explanatory code over excessive commenting
- Ensure all templates pass validation checks

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

*For detailed documentation and advanced usage, see the `docs/` directory.*
